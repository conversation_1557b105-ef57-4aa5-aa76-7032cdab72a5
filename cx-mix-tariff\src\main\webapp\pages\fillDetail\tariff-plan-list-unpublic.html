<!DOCTYPE html>
<html>
  <head>
    <title>未公示资费方案</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"
    />
    <!-- 基础的 css js 资源 -->
    <link
      rel="stylesheet"
      href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css"
    />
    <link
      rel="stylesheet"
      href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.4"
    />
    <link
      rel="stylesheet"
      href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.3"
    />
    <link rel="stylesheet" href="./common.css?v=20241127" />
    <link rel="stylesheet" href="/cx-mix-tariff/static/css/searchForm.css" />
    <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
    <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
    <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
    <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.1"></script>
    <script src="/cc-base/static/js/my_i18n.js?v=202111"></script>
    <script src="/cc-base/static/js/i18n.js?v=1"></script>
    <script src="/cc-base/static/cdn/axios@0.26.1/axios.min.js"></script>
    <style>
      .el-dialog {
        margin-top: 2vh !important;
      }

      .table2 {
        width: 100%;
        white-space: normal;
        line-height: 24px;
        height: calc(100vh - 315px);
        overflow: auto;
        color: #262626;
      }

      .el-tabs .el-tabs__header {
        margin-bottom: 0;
      }

      #renew-tariff-plan .search-form.grid-5 {
        gap: 16px 4px !important;
      }
    </style>
  </head>

  <body class="yq-page-full vue-box">
    <div id="renew-tariff-plan" class="flex yq-table-page">
      <div class="yq-card">
        <div class="card-header">
          <div class="head-title">未公示资费方案</div>
          <div class="yq-table-control">
            <el-button
              type="primary"
              plain
              size="small"
              @click="handleExport"
              v-if="permissions['cx-xty-tariff-plan-export']"
            >
              <i class="el-icon-download"></i>导出
            </el-button>
          </div>
        </div>
        <div
          class="card-content"
          v-auth:[permissions]="'cx-xty-tariff-plan-export;cx-xty-audit-orders;'"
        >
          <div class="search-box">
            <senior-search :show.sync="moreSearch">
              <el-form
                :inline="false"
                :model="searchForm"
                ref="form"
                class="search-form grid-5"
                label-width="70px"
                size="small"
              >
                <el-form-item label="资费名称" prop="tariffName">
                  <el-input
                    v-model.trim="searchForm.tariffName"
                    clearable
                  ></el-input>
                </el-form-item>
                 <el-form-item label="方案编号" prop="reportNo">
                  <el-input
                    v-model.trim="searchForm.reportNo"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="运营商" prop="ent">
                  <el-select
                    v-model="searchForm.ent"
                    placeholder="请选择"
                    filterable
                    clearable
                    :disabled="entCode ? true : false"
                  >
                    <el-option label="全行业" value=""></el-option>
                    <el-option
                      v-for="(label, value) in XTY_REPORTER_ENT"
                      :key="value"
                      :label="label"
                      :value="value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="订购省份" prop="provinceCode">
                  <el-select
                    v-model="searchForm.provinceCode"
                    placeholder="请选择"
                    filterable
                    clearable
                    :disabled="['01', '03'].includes(groupType)"
                  >
                    <el-option
                      v-for="(item, index) in reportObj"
                      :key="index"
                      :label="item.PROVINCE_NAME"
                      :value="item.PROVINCE_CODE"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <template v-if="moreSearch">
                  <el-form-item
                    :label="getI18nValue('出现日期')"
                    prop="appearTime"
                    label-width="80px"
                    :rules="[{ required: true, message: '请选择出现日期', trigger: 'change' }]"
                    style="grid-column-start: span 2"
                  >
                    <el-date-picker
                      v-model="searchForm.appearTime"
                      type="daterange"
                      align="right"
                      unlink-panels
                      :start-placeholder="getI18nValue('开始日期')"
                      :end-placeholder="getI18nValue('结束日期')"
                      value-format="yyyy-MM-dd"
                      clearable
                      style="width: 100%"
                    >
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item
                    :label="getI18nValue('首次出现日期')"
                    prop="firstTime"
                    label-width="100px"
                    style="grid-column-start: span 2"
                  >
                    <el-date-picker
                      v-model="searchForm.firstTime"
                      type="daterange"
                      align="right"
                      unlink-panels
                      :start-placeholder="getI18nValue('开始日期')"
                      :end-placeholder="getI18nValue('结束日期')"
                      value-format="yyyy-MM-dd"
                      clearable
                      style="width: 100%"
                    >
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item
                    :label="getI18nValue('最后出现日期')"
                    prop="endTime"
                    label-width="100px"
                    style="grid-column-start: span 2"
                  >
                    <el-date-picker
                      v-model="searchForm.endTime"
                      type="daterange"
                      align="right"
                      unlink-panels
                      :start-placeholder="getI18nValue('开始日期')"
                      :end-placeholder="getI18nValue('结束日期')"
                      value-format="yyyy-MM-dd"
                      clearable
                      style="width: 100%"
                    >
                    </el-date-picker>
                  </el-form-item>
                </template>
                <el-form-item class="btns" label-width="16px">
                  <el-button
                    type="primary"
                    plain
                    size="small"
                    icon="el-icon-refresh"
                    @click="handleReset"
                    >{{getI18nValue('重置')}}</el-button
                  >
                  <el-button
                    type="primary"
                    size="small"
                    icon="el-icon-search"
                    @click="getList(1)"
                    >{{getI18nValue('搜索')}}</el-button
                  >
                  <el-button
                    type="primary"
                    plain
                    size="small"
                    @click.stop="moreSearch = !moreSearch"
                  >
                    <img
                      src="/easitline-cdn/vue-yq/static/imgs/filter.png"
                      alt=""
                    />高级搜索
                  </el-button>
                </el-form-item>
              </el-form>
            </senior-search>
          </div>
          <div class="yq-table">
            <el-table
              ref="table"
              :data="tableData.data"
              style="width: 100%"
              height="100%"
              v-loading="tableData.loading"
              border
              @sort-change="sortChange"
              stripe
            >
              <el-table-column
                prop="tariffName"
                :label="getI18nValue('资费名称')"
                resizeable
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column
                prop="reportNo"
                :label="getI18nValue('方案编号')"
              ></el-table-column>
              <el-table-column
                prop="provinceName"
                :label="getI18nValue('订购省份')"
              ></el-table-column>
              <el-table-column
                prop="entName"
                :label="getI18nValue('运营商')"
              ></el-table-column>
              <el-table-column
                prop="firstAppearance"
                :label="getI18nValue('首次出现日期')"
              >
              </el-table-column>
              <el-table-column
                prop="lastAppearance"
                :label="getI18nValue('最后出现日期')"
              >
              </el-table-column>
            </el-table>
            <el-pagination
              background
              @current-change="onPageChange"
              @size-change="onPageSizeChange"
              :current-page="tableData.pageIndex"
              :page-size="tableData.pageSize"
              :page-sizes="[15, 30, 50, 100]"
              layout="total, prev, pager, next, jumper, sizes"
              :total="tableData.totalRow"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <script
      type="text/javascript"
      src="/cx-mix-tariff/static/js/time.js"
    ></script>
    <script src="./mixins.js?v=20250723"></script>
    <script>
      var qualityTask = new Vue({
        el: "#renew-tariff-plan",
        mixins: [mixins],
        data: function () {
          return {
            is_public_list: {
              Y: "公示",
              N: "不公示",
            },
            areaSelectType_list: {
              1: "全国",
              2: "指定省市",
              3: "排除省市",
            },

            tariffTypeDict: {
              // 提取字典
              1: "活跃在售",
              3: "续订",
              5: "未报送",
              // 2: "非活跃",
            },
            reportObj_b: [],
          };
        },

        methods: {
          getPayload(page) {
            this.tableData.pageIndex = page || this.tableData.pageIndex;
            function formatDateRange(range) {
              return range?.map?.((date) => date.replaceAll("-", "")) || [];
            }

            let appearTime = formatDateRange(this.searchForm.appearTime);
            let firstTime = formatDateRange(this.searchForm.firstTime);
            let endTime = formatDateRange(this.searchForm.endTime);

            return {
              pageType: "3",
              pageIndex: this.tableData.pageIndex,
              pageSize: this.tableData.pageSize,
              provinceCodes: this.searchForm.provinceCode,
              auditBeginDate: appearTime[0],
              auditEndDate: appearTime[1],
              ent: this.searchForm.ent,
              firstTime: firstTime.join(","),
              endTime: endTime.join(","),
              tariffName: this.searchForm.tariffName,
              reportNo: this.searchForm.reportNo,
            };
          },
          //查询列表
          getList: function (page) {
            this.$refs.form.validate((valid) => {
              if (!valid) {
                return false;
              }
              if (
                !Array.isArray(this.searchForm.appearTime) ||
                this.searchForm.appearTime.length !== 2
              ) {
                this.$message.error("请选择出现日期");
                return false;
              }

              // 默认使用活跃资费方案接口
              let url =
                "/cx-mix-tariff/webcall?action=tariffAuditDao.getUnpublicTariffList";
              const data = this.getPayload(page);
              this.tableData.loading = true;

              yq.remoteCall(url, data, (res) => {
                if (res.state == 1) {
                  this.tableData.data = res.data || [];
                  this.tableData.totalRow = res.total;
                } else {
                  this.$message.error(res.msg);
                }
                this.tableData.loading = false;
              });
            });
          },
          handleExport() {
            const data = this.getPayload();
            /*delete data.pageType
          delete data.pageIndex
          delete data.pageSize*/
            const url =
              "/cx-mix-tariff/servlet/tariffReport?action=exportUnpublicTariffList&" +
              $.param(data);
            yq.remoteCall(url, {}, (res) => {
              this.$message({
                type: res.state == 1 ? "success" : "error",
                message: res.msg,
              });
            });
            //this.$message.success('正在导出，请稍候')
          },

          getReportObjList() {
            yq.remoteCall(
              "/cx-mix-tariff/webcall?action=common.provinces",
              {},
              (res) => {
                this.reportObj_b = JSON.parse(JSON.stringify(res.data));
                this.reportObj_b.push({
                  PROVINCE_NAME: "集团",
                  PINYIN: "JT",
                });
              }
            );
          },
        },
        mounted() {
          this.getReportObjList();
          this.searchForm.ent = yq.q("ent", "");
          this.searchForm.provinceCode = yq.q("provinceCode", "");
          this.searchForm.appearTime = [
            getThisMonthStartDate(),
            getTodayDate(),
          ];
          if (yq.q("appearTime")) {
            this.searchForm.appearTime = decodeURIComponent(
              yq.q("appearTime")
            ).split(",");
            this.searchForm.endTime = "";
            this.searchForm.isRel = "";
          }
        },
      });
    </script>
  </body>
</html>
