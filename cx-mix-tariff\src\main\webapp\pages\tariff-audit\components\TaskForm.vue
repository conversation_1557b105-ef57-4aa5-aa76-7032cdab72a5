<template>
  <div style="height: 100%">
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      label-width="120px"
      class="drawer-content task-form">
      <!-- 第一步：基础信息 -->
      <div class="form-section">
        <div class="section-title">
          <i class="el-icon-edit"></i>
          <span>基础信息</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item
              label="任务名称"
              prop="taskName">
              <el-input
                v-model="form.taskName"
                placeholder="请输入任务名称"
                maxlength="100"
                show-word-limit></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="省份"
              prop="provinceCode">
              <el-select
                v-model="form.provinceCode"
                placeholder="请选择省份"
                style="width: 100%"
                @change="handleProvinceChange">
                <el-option
                  v-for="item in provinceOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="运营商"
              prop="entCode">
              <el-select
                v-model="form.entCode"
                placeholder="请选择运营商"
                style="width: 100%"
                @change="handleEntChange">
                <el-option
                  v-for="item in entOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 第二步：文件上传 -->
      <div class="form-section">
        <div class="section-title">
          <i class="el-icon-upload"></i>
          <span>文件上传</span>
        </div>
        <el-form-item
          label="ZIP文件"
          prop="zipFile">
          <el-upload
            ref="upload"
            :action="uploadUrl"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :on-change="handleFileChange"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :auto-upload="false"
            accept=".zip"
            :limit="1"
            class="upload-demo">
            <el-button
              slot="trigger"
              size="small"
              type="primary"
              icon="el-icon-folder-opened"
              >选择ZIP文件</el-button
            >
            <el-button
              style="margin-left: 10px"
              size="small"
              type="success"
              @click="submitUpload"
              :loading="uploading"
              icon="el-icon-upload2"
              >上传文件</el-button
            >
            <div
              slot="tip"
              class="el-upload__tip">
              <p><i class="el-icon-info"></i> 只能上传ZIP文件，且不超过50MB</p>
              <p><i class="el-icon-info"></i> ZIP文件中应包含Excel格式的数据文件</p>
            </div>
          </el-upload>
        </el-form-item>

        <div
          v-if="uploadedFile"
          class="upload-success">
          <el-alert
            title="文件上传成功"
            type="success"
            :closable="false"
            show-icon>
            <div slot="title">
              <i class="el-icon-circle-check"></i>
              文件上传成功
            </div>
            <p><strong>文件名：</strong>{{ uploadedFile.fileName }}</p>
            <p><strong>文件大小：</strong>{{ formatFileSize(uploadedFile.fileSize) }}</p>
            <p><strong>包含文件：</strong>{{ zipFileList.length }} 个Excel文件</p>
          </el-alert>
        </div>
      </div>

      <!-- 第三步：文件选择（上传成功后显示） -->
      <div
        class="form-section"
        v-if="showFileSelector">
        <div class="section-title">
          <i class="el-icon-document"></i>
          <span>文件配置</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="销售产品文件"
              prop="salesFileName">
              <el-select
                v-model="form.salesFileName"
                placeholder="请选择销售产品文件"
                style="width: 100%"
                @change="handleSalesFileChange"
                clearable>
                <el-option
                  v-for="file in zipFileList"
                  :key="file"
                  :label="file"
                  :value="file">
                  <span style="float: left">{{ file }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">Excel</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="订单文件"
              prop="ordersFileName">
              <el-select
                v-model="form.ordersFileName"
                placeholder="请选择订单文件"
                style="width: 100%"
                clearable>
                <el-option
                  v-for="file in availableOrderFiles"
                  :key="file"
                  :label="file"
                  :value="file">
                  <span style="float: left">{{ file }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">Excel</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 第四步：产品列配置（选择销售产品文件后显示） -->
      <div
        class="form-section"
        v-if="showProductColumns">
        <div class="section-title">
          <i class="el-icon-s-grid"></i>
          <span>产品列配置</span>
          <el-button
            v-if="form.salesFileName && !loadingColumns"
            type="text"
            size="mini"
            @click="loadExcelColumns"
            icon="el-icon-refresh"
            >刷新列信息</el-button
          >
        </div>

        <div
          v-if="loadingColumns"
          style="text-align: center; padding: 20px">
          <i class="el-icon-loading"></i>
          <span style="margin-left: 10px">正在解析Excel文件列信息...</span>
        </div>

        <div v-else>
          <!-- 第一行：产品ID和产品名称 -->
          <el-row :gutter="30">
            <el-col :span="12">
              <el-form-item
                label="产品ID列名"
                prop="productIdColumn"
                label-width="120px">
                <el-select
                  v-model="form.productIdColumn"
                  placeholder="请选择产品ID列"
                  style="width: 100%"
                  filterable>
                  <el-option
                    v-for="column in excelColumns"
                    :key="column"
                    :label="column"
                    :value="column"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="产品名称列名"
                prop="productNameColumn"
                label-width="120px">
                <el-select
                  v-model="form.productNameColumn"
                  placeholder="请选择产品名称列"
                  style="width: 100%"
                  filterable>
                  <el-option
                    v-for="column in excelColumns"
                    :key="column"
                    :label="column"
                    :value="column"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第二行：产品编号 -->
          <el-row :gutter="30">
            <el-col :span="12">
              <el-form-item
                label="产品编号列名"
                prop="productCodeColumn"
                label-width="120px">
                <el-select
                  v-model="form.productCodeColumn"
                  placeholder="请选择产品编号列"
                  style="width: 100%"
                  filterable>
                  <el-option
                    v-for="column in excelColumns"
                    :key="column"
                    :label="column"
                    :value="column"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 预留空间，可以后续添加其他字段 -->
            </el-col>
          </el-row>
        </div>

        <div
          v-if="excelColumns.length === 0 && !loadingColumns && form.salesFileName"
          style="text-align: center; padding: 20px; color: #909399">
          <i class="el-icon-warning"></i>
          <span style="margin-left: 10px">未能获取到Excel文件列信息，请检查文件格式或点击刷新重试</span>
        </div>
      </div>
    </el-form>

    <div class="drawer-footer">
      <el-button
        @click="handleCancel"
        size="medium"
        >取消</el-button
      >
      <el-button
        type="primary"
        @click="handleSubmit"
        :loading="submitting"
        size="medium">
        <i class="el-icon-check"></i>
        保存任务
      </el-button>
    </div>
  </div>
</template>

<script>
module.exports = {
  props: {
    provinceOptions: {
      type: Array,
      default: () => [],
    },
    entOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      form: {
        taskName: '',
        provinceCode: '',
        provinceName: '',
        entCode: '',
        entName: '',
        salesFileName: '',
        ordersFileName: '',
        productIdColumn: '',
        productNameColumn: '',
        productCodeColumn: '',
        zipFile: null, // 添加zipFile字段用于表单验证
      },
      rules: {
        taskName: [
          { required: true, message: '请输入任务名称', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' },
        ],
        provinceCode: [{ required: true, message: '请选择省份', trigger: 'change' }],
        entCode: [{ required: true, message: '请选择运营商', trigger: 'change' }],
        zipFile: [{ required: true, message: '请上传ZIP文件', trigger: 'change' }],
        salesFileName: [{ validator: this.validateFileSelection, trigger: 'change' }],
        ordersFileName: [{ validator: this.validateFileSelection, trigger: 'change' }],
        productIdColumn: [{ validator: this.validateProductColumn, trigger: 'change' }],
        productNameColumn: [{ validator: this.validateProductColumn, trigger: 'change' }],
        productCodeColumn: [{ validator: this.validateProductColumn, trigger: 'change' }],
      },
      uploadUrl: '/cx-mix-tariff/servlet/tariffAuditTask?action=uploadFile',
      fileList: [],
      uploading: false,
      submitting: false,
      uploadedFile: null,
      zipFileList: [], // zip文件中的文件列表
      showFileSelector: false, // 是否显示文件选择器
      excelColumns: [], // Excel文件的列名列表
      loadingColumns: false, // 是否正在加载列信息
    }
  },
  computed: {
    // 可用的订单文件列表（排除已选择的销售产品文件）
    availableOrderFiles() {
      return this.zipFileList.filter((file) => file !== this.form.salesFileName)
    },
    // 是否显示产品列配置
    showProductColumns() {
      return this.showFileSelector && this.form.salesFileName
    },
    // 是否显示订单列配置
    showOrderColumns() {
      return this.showFileSelector && this.form.ordersFileName
    },
  },
  methods: {
    // 验证文件选择（销售产品文件和订单文件至少选择一个）
    validateFileSelection(rule, value, callback) {
      if (!this.showFileSelector) {
        callback()
        return
      }

      if (!this.form.salesFileName && !this.form.ordersFileName) {
        callback(new Error('销售产品文件和订单文件至少选择一个'))
      } else {
        // 清除另一个字段的验证错误
        this.$nextTick(() => {
          if (rule.field === 'salesFileName' && this.form.ordersFileName) {
            this.$refs.form.clearValidate(['ordersFileName'])
          } else if (rule.field === 'ordersFileName' && this.form.salesFileName) {
            this.$refs.form.clearValidate(['salesFileName'])
          }
        })
        callback()
      }
    },

    // 验证产品列配置（选择销售产品文件时必填）
    validateProductColumn(rule, value, callback) {
      if (!this.form.salesFileName) {
        callback()
        return
      }

      if (!value || value.trim() === '') {
        const fieldNames = {
          productIdColumn: '产品ID列名',
          productNameColumn: '产品名称列名',
          productCodeColumn: '产品编号列名',
        }
        callback(new Error(`请输入${fieldNames[rule.field]}`))
      } else {
        callback()
      }
    },
    // 省份变化
    handleProvinceChange(value) {
      const province = this.provinceOptions.find((item) => item.value === value)
      this.form.provinceName = province ? province.label : ''
    },

    // 运营商变化
    handleEntChange(value) {
      const ent = this.entOptions.find((item) => item.value === value)
      this.form.entName = ent ? ent.label : ''
    },

    // 文件选择变化
    handleFileChange(file, fileList) {
      this.fileList = fileList
    },

    // 上传前检查
    beforeUpload(file) {
      const isZip = file.type === 'application/zip' || file.name.toLowerCase().endsWith('.zip')
      const isLt50M = file.size / 1024 / 1024 < 50

      if (!isZip) {
        this.$message.error('只能上传ZIP格式的文件!')
        return false
      }
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过 50MB!')
        return false
      }
      return true
    },

    // 提交上传
    submitUpload() {
      // 检查是否有文件选择
      if (!this.fileList || this.fileList.length === 0) {
        this.$message.warning('请先选择要上传的文件')
        return
      }

      // 检查文件状态
      const hasValidFile = this.fileList.some((file) => file.status !== 'fail')
      if (!hasValidFile) {
        this.$message.warning('请选择有效的ZIP文件')
        return
      }

      this.uploading = true
      this.$refs.upload.submit()
    },

    // 上传成功
    handleUploadSuccess(response) {
      this.uploading = false
      if (response.state == 1) {
        this.uploadedFile = response.data
        // 同步到表单字段，用于验证
        this.form.zipFile = response.data
        this.form.zipFileName = this.fileList[0].name
        this.$message.success('文件上传成功')

        // 获取zip文件列表
        if (response.data && response.data.fileList) {
          this.zipFileList = response.data.fileList
          this.showFileSelector = true
        }

        // 触发zipFile字段验证
        this.$nextTick(() => {
          this.$refs.form.validateField(['zipFile'])
        })
      } else {
        this.$message.error(response.msg || '文件上传失败')
      }
    },

    // 上传失败
    handleUploadError() {
      this.uploading = false
      this.$message.error('文件上传失败')
    },

    // 销售产品文件选择变化
    handleSalesFileChange(value) {
      this.form.salesFileName = value

      // 如果订单文件和销售产品文件相同，清空订单文件
      if (this.form.ordersFileName === value) {
        this.form.ordersFileName = ''
      }

      // 如果没有选择订单文件，自动选择第一个可用文件
      if (!this.form.ordersFileName && this.availableOrderFiles.length > 0) {
        this.form.ordersFileName = this.availableOrderFiles[0]
      }

      // 清空产品列配置（如果取消选择销售产品文件）
      if (!value) {
        this.form.productIdColumn = ''
        this.form.productNameColumn = ''
        this.form.productCodeColumn = ''
        this.excelColumns = []
      } else {
        // 选择了销售产品文件，自动加载Excel列信息
        this.loadExcelColumns()
      }

      // 触发验证
      this.$nextTick(() => {
        this.$refs.form.validateField(['salesFileName', 'ordersFileName', 'productIdColumn', 'productNameColumn', 'productCodeColumn'])
      })
    },

    // 加载Excel文件列信息
    loadExcelColumns() {
      if (!this.form.salesFileName || !this.uploadedFile) {
        return
      }

      this.loadingColumns = true
      this.excelColumns = []

      // 调用后端接口获取Excel列信息
      const params = {
        filePath: this.uploadedFile.filePath || this.uploadedFile.fileName,
        productFileName: this.form.salesFileName,
        orderFileName: this.form.ordersFileName,
      }

      // 使用axios发送请求
      axios
        .post('/cx-mix-tariff/servlet/tariffAuditTask?action=getExcelColumns', params)
        .then((response) => {
          this.loadingColumns = false
          if (response.data && response.data.state === 1) {
            this.excelColumns = response.data.data || []
            if (this.excelColumns.length > 0) {
              this.$message.success(`成功获取到 ${this.excelColumns.length} 个列名`)
            } else {
              this.$message.warning('Excel文件中没有找到列名')
            }
          } else {
            this.$message.error(response.data.msg || '获取Excel列信息失败')
          }
        })
        .catch((error) => {
          this.loadingColumns = false
          console.error('获取Excel列信息失败:', error)
          this.$message.error('获取Excel列信息失败，请检查网络连接')
        })
    },

    // 提交表单
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.uploadedFile) {
            this.$message.warning('请先上传ZIP文件')
            return
          }

          // 额外验证：确保至少选择了一个文件
          if (!this.form.salesFileName && !this.form.ordersFileName) {
            this.$message.error('请至少选择销售产品文件或订单文件中的一个')
            return
          }

          this.submitting = true

          // 构建提交数据
          const submitData = {
            taskName: this.form.taskName,
            provinceCode: this.form.provinceCode,
            provinceName: this.form.provinceName,
            entCode: this.form.entCode,
            entName: this.form.entName,
            salesFileName: this.form.salesFileName || '',
            ordersFileName: this.form.ordersFileName || '',
            productIdColumn: this.form.productIdColumn || '',
            productNameColumn: this.form.productNameColumn || '',
            productCodeColumn: this.form.productCodeColumn || '',
            // 文件信息
            fileName: this.uploadedFile.fileName,
            filePath: this.uploadedFile.filePath,
            fileSize: this.uploadedFile.fileSize,
          }

          // 调用后端接口
          yq.remoteCall('/cx-mix-tariff/servlet/tariffAuditTask?action=add', submitData, (response) => {
            this.submitting = false
            if (response && response.state === 1) {
              this.$message.success(response.msg || '任务创建成功')
              // 通知父组件关闭抽屉并刷新列表
              this.$emit('success')
            } else {
              this.$message.error(response.msg || '任务创建失败，请重试')
            }
          }).catch((error) => {
            this.submitting = false
            console.error('提交任务失败:', error)
            this.$message.error('网络错误，请检查网络连接后重试')
          })
        } else {
          this.$message.error('请检查表单填写是否正确')
          return false
        }
      })
    },

    // 取消
    handleCancel() {
      this.$refs.form.resetFields()
      this.fileList = []
      this.uploadedFile = null
      this.form.zipFile = null // 清空zipFile字段
      this.zipFileList = []
      this.showFileSelector = false
      this.excelColumns = []
      this.loadingColumns = false
      this.$emit('cancel')
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '0 B'
      if (size < 1024) return size + ' B'
      if (size < 1024 * 1024) return (size / 1024).toFixed(2) + ' KB'
      return (size / (1024 * 1024)).toFixed(2) + ' MB'
    },
  },
}
</script>

<style></style>
